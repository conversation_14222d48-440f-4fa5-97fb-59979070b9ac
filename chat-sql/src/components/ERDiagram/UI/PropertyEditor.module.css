.propertyEditor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  margin-bottom: 16px;
}

.title {
  margin: 0 !important;
  color: var(--primary-text);
}

.section {
  margin-bottom: 16px;
  border: 1px solid var(--card-border);
}

.section:last-child {
  margin-bottom: 0;
  flex: 1;
  overflow: hidden;
}

.section .ant-card-body {
  padding: 12px;
}

.section .ant-list {
  max-height: 300px;
  overflow-y: auto;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--secondary-text);
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.primaryKeyIcon {
  color: #d32f2f;
  font-size: 16px;
}

.pkBadge {
  background: #ffeaea;
  color: #d32f2f;
  font-size: 11px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  letter-spacing: 0.5px;
}

.requiredBadge {
  background: #fff7e6;
  color: #fa8c16;
  font-size: 11px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
}

.description {
  font-size: 12px;
  line-height: 1.4;
  max-width: 200px;
  word-break: break-word;
}

/* 弱实体集的DIS标签样式 */
[data-theme="light"] .pkBadge {
  background: #ffeaea;
  color: #d32f2f;
}

[data-theme="dark"] .pkBadge {
  background: #2a1f1f;
  color: #ff6b6b;
}

/* 必填标签样式 */
[data-theme="light"] .requiredBadge {
  background: #fff7e6;
  color: #fa8c16;
}

[data-theme="dark"] .requiredBadge {
  background: #2a2416;
  color: #ffa940;
}

/* 列表项样式 */
.section .ant-list-item {
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.section .ant-list-item:last-child {
  border-bottom: none;
}

.section .ant-list-item-meta-title {
  margin-bottom: 4px;
}

.section .ant-list-item-meta-description {
  color: var(--secondary-text);
}

.section .ant-list-item-action {
  margin-left: 8px;
}

/* 表单样式 */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item:last-child {
  margin-bottom: 0;
}

/* 开关样式 */
.ant-switch {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section .ant-card-body {
    padding: 8px;
  }
  
  .section .ant-list-item {
    padding: 6px 0;
  }
  
  .description {
    max-width: 150px;
  }
}

/* 暗色主题支持 */
[data-theme="dark"] .propertyEditor {
  color: var(--primary-text);
}

[data-theme="dark"] .section {
  border-color: var(--card-border);
  background: var(--card-bg);
}

[data-theme="dark"] .emptyState {
  color: var(--secondary-text);
}

[data-theme="dark"] .primaryKeyIcon {
  color: #ff6b6b;
}

[data-theme="dark"] .section .ant-list-item {
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .section .ant-list-item-meta-description {
  color: var(--secondary-text);
}
