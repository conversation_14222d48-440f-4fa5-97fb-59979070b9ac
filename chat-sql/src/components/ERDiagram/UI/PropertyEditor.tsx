'use client';

import React, { useState } from 'react';
import {
  Card,
  Button,
  Input,
  Select,
  Switch,
  Space,
  Typography,
  Divider,
  List,
  Popconfirm,
  Form,
  Modal,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  KeyOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useERDiagramContext } from '@/contexts/ERDiagramContext';
import { EREntity, ERRelationship, ERAttribute } from '@/types/erDiagram';
import styles from './PropertyEditor.module.css';

const { Text, Title } = Typography;
const { Option } = Select;

interface PropertyEditorProps {
  selectedElement: EREntity | ERRelationship | null;
  onUpdateEntity: (id: string, updates: Partial<EREntity>) => void;
  onUpdateRelationship: (id: string, updates: Partial<ERRelationship>) => void;
}

// 数据类型选项
const DATA_TYPES = [
  'VARCHAR(50)',
  'VARCHAR(100)',
  'VARCHAR(255)',
  'INT',
  'BIGINT',
  'DECIMAL(10,2)',
  'DECIMAL(5,2)',
  'BOOLEAN',
  'DATE',
  'DATETIME',
  'TIMESTAMP',
  'TEXT',
  'LONGTEXT'
];

const PropertyEditor: React.FC<PropertyEditorProps> = ({
  selectedElement,
  onUpdateEntity,
  onUpdateRelationship
}) => {
  const [isAddingAttribute, setIsAddingAttribute] = useState(false);
  const [editingAttribute, setEditingAttribute] = useState<ERAttribute | null>(null);
  const [form] = Form.useForm();

  // 判断选中元素类型
  const isEntity = selectedElement && 'attributes' in selectedElement && !('connections' in selectedElement);
  const isRelationship = selectedElement && 'connections' in selectedElement;

  // 添加新属性
  const handleAddAttribute = (values: any) => {
    if (!selectedElement) return;

    const newAttribute: ERAttribute = {
      id: `attr_${Date.now()}`,
      name: values.name,
      dataType: values.dataType,
      isPrimaryKey: values.isPrimaryKey || false,
      isRequired: values.isRequired || false,
      description: values.description || ''
    };

    if (isEntity) {
      const entity = selectedElement as EREntity;
      const updatedAttributes = [...(entity.attributes || []), newAttribute];
      onUpdateEntity(entity.id, { attributes: updatedAttributes });
    } else if (isRelationship) {
      const relationship = selectedElement as ERRelationship;
      const updatedAttributes = [...(relationship.attributes || []), newAttribute];
      onUpdateRelationship(relationship.id, { attributes: updatedAttributes });
    }

    setIsAddingAttribute(false);
    form.resetFields();
    message.success('属性添加成功');
  };

  // 编辑属性
  const handleEditAttribute = (values: any) => {
    if (!selectedElement || !editingAttribute) return;

    const updatedAttribute: ERAttribute = {
      ...editingAttribute,
      name: values.name,
      dataType: values.dataType,
      isPrimaryKey: values.isPrimaryKey || false,
      isRequired: values.isRequired || false,
      description: values.description || ''
    };

    if (isEntity) {
      const entity = selectedElement as EREntity;
      const updatedAttributes = entity.attributes.map(attr =>
        attr.id === editingAttribute.id ? updatedAttribute : attr
      );
      onUpdateEntity(entity.id, { attributes: updatedAttributes });
    } else if (isRelationship) {
      const relationship = selectedElement as ERRelationship;
      const updatedAttributes = (relationship.attributes || []).map(attr =>
        attr.id === editingAttribute.id ? updatedAttribute : attr
      );
      onUpdateRelationship(relationship.id, { attributes: updatedAttributes });
    }

    setEditingAttribute(null);
    form.resetFields();
    message.success('属性更新成功');
  };

  // 删除属性
  const handleDeleteAttribute = (attributeId: string) => {
    if (!selectedElement) return;

    if (isEntity) {
      const entity = selectedElement as EREntity;
      const updatedAttributes = entity.attributes.filter(attr => attr.id !== attributeId);
      onUpdateEntity(entity.id, { attributes: updatedAttributes });
    } else if (isRelationship) {
      const relationship = selectedElement as ERRelationship;
      const updatedAttributes = (relationship.attributes || []).filter(attr => attr.id !== attributeId);
      onUpdateRelationship(relationship.id, { attributes: updatedAttributes });
    }

    message.success('属性删除成功');
  };

  // 更新实体描述
  const handleUpdateDescription = (description: string) => {
    if (!selectedElement) return;

    if (isEntity) {
      onUpdateEntity(selectedElement.id, { description });
    } else if (isRelationship) {
      onUpdateRelationship(selectedElement.id, { description });
    }
  };

  // 切换弱实体集状态
  const handleToggleWeakEntity = (isWeak: boolean) => {
    if (!selectedElement || !isEntity) return;
    onUpdateEntity(selectedElement.id, { isWeakEntity: isWeak });
  };

  if (!selectedElement) {
    return (
      <div className={styles.emptyState}>
        <SettingOutlined className={styles.emptyIcon} />
        <Text type="secondary">选择一个节点来编辑其属性</Text>
      </div>
    );
  }

  const attributes = isEntity 
    ? (selectedElement as EREntity).attributes 
    : (selectedElement as ERRelationship).attributes || [];

  return (
    <div className={styles.propertyEditor}>
      <div className={styles.header}>
        <Title level={4} className={styles.title}>
          {isEntity ? '实体属性' : '关系属性'}
        </Title>
        <Text type="secondary">{selectedElement.name}</Text>
      </div>

      <Divider />

      {/* 基本信息 */}
      <Card size="small" title="基本信息" className={styles.section}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>名称：</Text>
            <Text>{selectedElement.name}</Text>
          </div>
          
          <div>
            <Text strong>描述：</Text>
            <Input.TextArea
              value={selectedElement.description || ''}
              onChange={(e) => handleUpdateDescription(e.target.value)}
              placeholder="输入描述信息"
              rows={2}
              style={{ marginTop: 4 }}
            />
          </div>

          {isEntity && (
            <div>
              <Text strong>弱实体集：</Text>
              <Switch
                checked={(selectedElement as EREntity).isWeakEntity || false}
                onChange={handleToggleWeakEntity}
                style={{ marginLeft: 8 }}
              />
            </div>
          )}
        </Space>
      </Card>

      {/* 属性列表 */}
      <Card 
        size="small" 
        title="属性列表" 
        className={styles.section}
        extra={
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => setIsAddingAttribute(true)}
          >
            添加属性
          </Button>
        }
      >
        <List
          dataSource={attributes}
          renderItem={(attribute) => (
            <List.Item
              actions={[
                <Button
                  key="edit"
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => {
                    setEditingAttribute(attribute);
                    form.setFieldsValue(attribute);
                  }}
                />,
                <Popconfirm
                  key="delete"
                  title="确定删除此属性？"
                  onConfirm={() => handleDeleteAttribute(attribute.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                  />
                </Popconfirm>
              ]}
            >
              <List.Item.Meta
                avatar={
                  attribute.isPrimaryKey ? (
                    <KeyOutlined className={styles.primaryKeyIcon} />
                  ) : null
                }
                title={
                  <Space>
                    <Text strong>{attribute.name}</Text>
                    {attribute.isPrimaryKey && (
                      <Text type="danger" className={styles.pkBadge}>
                        {isEntity && (selectedElement as EREntity).isWeakEntity ? 'DIS' : 'PK'}
                      </Text>
                    )}
                    {attribute.isRequired && (
                      <Text type="warning" className={styles.requiredBadge}>必填</Text>
                    )}
                  </Space>
                }
                description={
                  <Space direction="vertical" size={0}>
                    <Text type="secondary">{attribute.dataType}</Text>
                    {attribute.description && (
                      <Text type="secondary" className={styles.description}>
                        {attribute.description}
                      </Text>
                    )}
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      {/* 添加属性模态框 */}
      <Modal
        title="添加属性"
        open={isAddingAttribute}
        onCancel={() => {
          setIsAddingAttribute(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="添加"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddAttribute}
        >
          <Form.Item
            name="name"
            label="属性名称"
            rules={[{ required: true, message: '请输入属性名称' }]}
          >
            <Input placeholder="输入属性名称" />
          </Form.Item>

          <Form.Item
            name="dataType"
            label="数据类型"
            rules={[{ required: true, message: '请选择数据类型' }]}
          >
            <Select placeholder="选择数据类型">
              {DATA_TYPES.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="isPrimaryKey" valuePropName="checked">
            <Switch /> <Text>主键</Text>
          </Form.Item>

          <Form.Item name="isRequired" valuePropName="checked">
            <Switch /> <Text>必填</Text>
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input.TextArea placeholder="输入属性描述" rows={2} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑属性模态框 */}
      <Modal
        title="编辑属性"
        open={!!editingAttribute}
        onCancel={() => {
          setEditingAttribute(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleEditAttribute}
        >
          <Form.Item
            name="name"
            label="属性名称"
            rules={[{ required: true, message: '请输入属性名称' }]}
          >
            <Input placeholder="输入属性名称" />
          </Form.Item>

          <Form.Item
            name="dataType"
            label="数据类型"
            rules={[{ required: true, message: '请选择数据类型' }]}
          >
            <Select placeholder="选择数据类型">
              {DATA_TYPES.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="isPrimaryKey" valuePropName="checked">
            <Switch /> <Text>主键</Text>
          </Form.Item>

          <Form.Item name="isRequired" valuePropName="checked">
            <Switch /> <Text>必填</Text>
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input.TextArea placeholder="输入属性描述" rows={2} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PropertyEditor;
