/* 实体节点样式 */
.entityNode {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  min-width: 260px;
  max-width: 340px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease-out;
  overflow: hidden;
  position: relative;
}

/* 弱实体集双边框样式 */
.entityNode.weakEntity {
  border: 2px dashed #cbc8c8;
}

.entityNode.weakEntity::before {
  content: '';
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border: 3px dashed #cbc8c8;
  border-radius: 8px;
  pointer-events: none;
  z-index: 1;
}

.entityNode:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.entityNode.selected {
  border-color: #2196f3;
  box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.3);
}

/* 弱实体集选中状态 */
.entityNode.weakEntity.selected {
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

.entityNode.weakEntity.selected::before {
  border-color: #2196f3;
}

/* 节点选中状态（用于属性编辑） */
.entityNode.nodeSelected {
  border-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.3);
}

.entityNode.weakEntity.nodeSelected {
  border-color: #52c41a;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.3);
}

.entityNode.weakEntity.nodeSelected::before {
  border-color: #52c41a;
}

/* 拖拽中的节点样式 */
.entityNode.dragging {
  transform: scale(1.02);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.header {
  position: relative;
  min-height: 24px;
  padding: 12px 32px 12px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: var(--primary-text);
}

.title {
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--primary-text);
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.title:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

/* 内联编辑器样式 */
.inlineEditor {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  min-width: 120px;
  max-width: 200px;
}

.attributesList {
  padding: 8px;
  color: var(--secondary-text);
}

.attribute {
  display: flex;
  align-items: center;
  padding: 8px 0 8px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  background: none;
  padding-left: 12px;
}

.attribute:last-child {
  border-bottom: none;
}

.attribute:hover {
  background-color: #f5f5f5;
}

/* 主键行左侧橙色竖线 */
/* 移除.pkStripe相关样式 */

.attributeName {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-left: 0;
  flex: 1 1 auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dataType {
  font-size: 14px;
  color: #666;
  margin-left: auto;
  margin-right: 8px;
  font-style: normal;
  white-space: nowrap;
}

.attributeBadges {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pkBadge {
  background: #ffeaea;
  color: #d32f2f;
  font-size: 13px;
  font-weight: bold;
  padding: 2px 10px 2px 10px;
  border-radius: 8px;
  margin-left: 2px;
  letter-spacing: 1px;
  display: inline-block;
}

.disBadge {
  background: #1976d2;
  color: #fff;
  border-radius: 8px;
  padding: 0 8px;
  margin-left: 8px;
  font-size: 0.85em;
  font-weight: bold;
}

.required {
  color: #f44336;
  font-weight: bold;
  font-size: 16px;
  margin-left: 2px;
  display: inline-block;
}

.handle {
  width: 8px !important;
  height: 8px !important;
  background: #2196f3 !important;
  border: 2px solid white !important;
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
}

.handle:hover {
  width: 12px !important;
  height: 12px !important;
  background: #1976d2 !important;
}

/* 约束图标样式 */
.constraintIcon {
  position: absolute;
  right: -24px;
  top: 50%;
  transform: translateY(-50%) translateX(0);
  
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: rgb(92, 90, 90);
  font-size: 12px;
  cursor: help;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  opacity: 0;
}

.entityNode:hover .constraintIcon {
  right: 8px;
  opacity: 1;
  transform: translateY(-50%) translateX(0);
}

.constraintIcon:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .entityNode {
    min-width: 160px;
    max-width: 240px;
  }
  
  .title {
    font-size: 14px;
  }
  
  .attributeName {
    font-size: 13px;
  }
  
  .dataType {
    font-size: 11px;
  }
  
  .attribute {
    padding: 6px 12px;
  }
}

/* 暗色主题支持 */
[data-theme="dark"] .entityNode {
  background: var(--card-bg);
  border-color: var(--border-color);
  color: var(--primary-text);
}

/* 暗色主题下的弱实体集样式 */
[data-theme="dark"] .entityNode.weakEntity {
  border-color: var(--border-color);
}

[data-theme="dark"] .entityNode.weakEntity::before {
  border-color: var(--border-color);
}

[data-theme="dark"] .entityNode:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .header {
  color: var(--primary-text);
}

[data-theme="dark"] .attribute {
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .attribute:hover {
  background-color: var(--hover-bg);
}

[data-theme="dark"] .attributeName {
  color: var(--primary-text);
}

[data-theme="dark"] .dataType {
  background: var(--secondary-bg);
  color: var(--secondary-text);
}
